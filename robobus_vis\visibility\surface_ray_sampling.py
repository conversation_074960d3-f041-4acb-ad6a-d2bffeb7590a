import numpy as np


def visibility_via_ray_sampling(box3d, cam, depth_img: np.ndarray, samples=2048):
    """Fallback visibility estimate using surface ray sampling.
    box3d: dict with 'surface_pts' Nx3 precomputed.
    """
    H, W = depth_img.shape
    pts = box3d['surface_pts']
    if len(pts) == 0:
        return 0.0, {"samples": 0}
    # subsample if needed
    if samples > 0 and len(pts) > samples:
        idx = np.random.choice(len(pts), size=samples, replace=False)
        pts = pts[idx]
    uvz = cam.project(pts)
    u = np.round(uvz[:,0]).astype(int)
    v = np.round(uvz[:,1]).astype(int)
    z = uvz[:,2]
    in_img = (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u, v, z = u[in_img], v[in_img], z[in_img]
    if len(u) == 0:
        return 0.0, {"samples": 0}
    D = depth_img[v, u]
    # if D exists and D < z -> occluded; if no D or D>=z -> visible
    visible = (~np.isfinite(D)) | (D >= z)
    ratio = float(visible.sum()) / float(len(visible))
    return ratio, {"samples": int(len(visible))}
