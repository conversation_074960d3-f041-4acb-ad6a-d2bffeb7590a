from typing import Dict, Any
from pathlib import Path
import re
import numpy as np
from ..calib.transforms import quat_to_R, make_T

"""Parser for calibrated_sensor.pb.txt in Protobuf TextFormat based on provided snippet.
Expected fields per sensor_info:
- name: "H60L-..."
- type: SENSING_60 / SENSING_120 / etc. (unused for math)
- orientation: FRONT/REAR/LEFT/RIGHT etc. (optional)
- extrinsic: translation{x,y,z} + rotation{x,y,z,w}  (quaternion, order shown in sample)
- intrinsic: width,height,matrix:9 entries row-major
- distcoeff: distort_matrix: up to 5 entries (k1,k2,p1,p2,k3)
"""

SENSOR_BLOCK_RE = re.compile(r"sensor_info\s*\{([\s\S]*?)\}")
NAME_RE = re.compile(r"name:\s*\"([^\"]+)\"")
WIDTH_RE = re.compile(r"width:\s*(\d+)")
HEIGHT_RE = re.compile(r"height:\s*(\d+)")
MATRIX_RE = re.compile(r"matrix:\s*([-+eE0-9\.]+)")
DISTORT_RE = re.compile(r"distort_matrix:\s*([-+eE0-9\.]+)")
# translation / rotation
TX_RE = re.compile(r"translation\s*\{[\s\S]*?x:\s*([-+eE0-9\.]+)[\s\S]*?y:\s*([-+eE0-9\.]+)[\s\S]*?z:\s*([-+eE0-9\.]+)[\s\S]*?\}")
ROT_RE = re.compile(r"rotation\s*\{[\s\S]*?x:\s*([-+eE0-9\.]+)[\s\S]*?y:\s*([-+eE0-9\.]+)[\s\S]*?z:\s*([-+eE0-9\.]+)[\s\S]*?w:\s*([-+eE0-9\.]+)[\s\S]*?\}")


def parse_calibrated_sensor_pb_txt(path: str) -> "Calibrations":
    text = Path(path).read_text(encoding='utf-8', errors='ignore')
    cams: Dict[str, Dict[str, Any]] = {}
    for block in SENSOR_BLOCK_RE.findall(text):
        name_m = NAME_RE.search(block)
        if not name_m:
            continue
        name = name_m.group(1)
        # extrinsic
        t_m = TX_RE.search(block)
        r_m = ROT_RE.search(block)
        if not (t_m and r_m):
            continue
        tx, ty, tz = map(float, t_m.groups())
        rx, ry, rz, rw = map(float, r_m.groups())
        R = quat_to_R(rx, ry, rz, rw)
        T_base_cam = make_T(R, np.array([tx, ty, tz], dtype=float))
        # intrinsic
        w_m = WIDTH_RE.search(block)
        h_m = HEIGHT_RE.search(block)
        width = int(w_m.group(1)) if w_m else 0
        height = int(h_m.group(1)) if h_m else 0
        mats = [float(m) for m in MATRIX_RE.findall(block)]
        K = np.eye(3)
        if len(mats) >= 9:
            K = np.array(mats[:9], dtype=float).reshape(3,3)
        # distortion
        dcoeff = [float(d) for d in DISTORT_RE.findall(block)]
        dist = np.array(dcoeff, dtype=float) if dcoeff else None
        # store
        cams[name] = {
            "name": name,
            "K": K,
            "dist": dist,
            "width": width,
            "height": height,
            "T_base_cam": T_base_cam,
        }
    return Calibrations(cams)


class Calibrations:
    def __init__(self, cams: Dict[str, Dict[str, Any]]):
        self.cams = cams

    def for_camera(self, cam_name: str) -> Dict[str, Any]:
        return self.cams[cam_name]

    def cameras(self):
        return list(self.cams.keys())
