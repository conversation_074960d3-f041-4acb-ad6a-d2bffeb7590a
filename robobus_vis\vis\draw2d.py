import numpy as np
import cv2

COLORS = {
    'box': (0, 255, 0),
    'text_bg': (0, 0, 0),
    'text_fg': (255, 255, 255),
    'proj': (0, 200, 255),
    'visible': (0, 255, 0),
    'occluded': (0, 0, 255),
}


def put_text(img, text, org=(10,20)):
    cv2.rectangle(img, (org[0]-3, org[1]-15), (org[0]+300, org[1]+5), COLORS['text_bg'], -1)
    cv2.putText(img, text, org, cv2.FONT_HERSHEY_SIMPLEX, 0.5, COLORS['text_fg'], 1, cv2.LINE_AA)


def draw_projected_points(img, cam, pts_base, radius=1):
    uvz = cam.project(pts_base)
    H, W = img.shape[:2]
    for u, v, z in uvz:
        if z <= 0:
            continue
        ui, vi = int(round(u)), int(round(v))
        if 0 <= ui < W and 0 <= vi < H:
            cv2.circle(img, (ui,vi), radius, COLORS['proj'], -1)


def draw_box_projections(img, cam, corners):
    H, W = img.shape[:2]
    uvz = cam.project(corners)
    pts = []
    for u, v, z in uvz:
        if z <= 0:
            pts.append(None)
            continue
        ui, vi = int(round(u)), int(round(v))
        if 0 <= ui < W and 0 <= vi < H:
            pts.append((ui,vi))
        else:
            pts.append(None)
    # draw edges if both endpoints valid
    edges = [(0,1),(1,2),(2,3),(3,0),(4,5),(5,6),(6,7),(7,4),(0,4),(1,5),(2,6),(3,7)]
    for i,j in edges:
        if pts[i] is not None and pts[j] is not None:
            cv2.line(img, pts[i], pts[j], COLORS['box'], 2)


def overlay_visibility_score(img, score: float, org=(10,40)):
    put_text(img, f"vis={score:.2f}", org)


def make_mosaic(images, cols=3, pad=4, bg=(30,30,30)):
    import math
    if not images:
        return None
    Hs = [im.shape[0] for im in images]
    Ws = [im.shape[1] for im in images]
    H = max(Hs)
    W = max(Ws)
    rows = math.ceil(len(images)/cols)
    canvas = np.full((rows*H + (rows+1)*pad, cols*W + (cols+1)*pad, 3), bg, dtype=np.uint8)
    for idx, im in enumerate(images):
        r = idx // cols
        c = idx % cols
        y0 = pad + r*(H+pad)
        x0 = pad + c*(W+pad)
        h, w = im.shape[:2]
        canvas[y0:y0+h, x0:x0+w] = im
    return canvas
