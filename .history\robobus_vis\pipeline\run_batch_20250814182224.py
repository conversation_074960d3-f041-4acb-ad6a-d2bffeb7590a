import argparse
from pathlib import Path
import json
import yaml
import numpy as np
import cv2
from ..io.dataset_loader import ClipDataset
from ..io.calib_parser import parse_calibrated_sensor_pb_txt
from ..io.pose_loader import PoseStream
from ..calib.camera_model import Camera
from ..calib.transforms import transform_points
from ..geometry.depth_tools import pointcloud_to_depth
from ..geometry.box3d import sample_box_surface, box_corners
from ..visibility.per_camera_pixel_ratio import visibility_via_zbuffer
from ..visibility.surface_ray_sampling import visibility_via_ray_sampling
from ..visibility.fusion import fuse_camera_visibility, to_occlusion_level
from ..visibility.diagnostics import visibility_confidence
from ..visibility.sync_compensation import compensate_points_to_time
from ..vis.draw2d import draw_projected_points, draw_box_projections, overlay_visibility_score, make_mosaic
from ..vis.exporters import save_image

# End-to-end batch with projection visualization and sync fields


def load_pcd_xyz(pcd_path: Path) -> np.ndarray:
    try:
        pts = np.loadtxt(str(pcd_path), usecols=(0,1,2))
        if pts.ndim == 1:
            pts = pts.reshape(1, -1)
        return pts.astype(float)
    except Exception:
        return np.empty((0,3), dtype=float)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--clip_dir', required=True)
    parser.add_argument('--config', default=str(Path(__file__).resolve().parents[2] / 'configs' / 'default.yaml'))
    parser.add_argument('--save_dir', default='./outputs')
    parser.add_argument('--viz_cam', action='store_true')
    parser.add_argument('--method', default='zbuffer', choices=['zbuffer','rays','auto'])
    parser.add_argument('--samples', type=int, default=2048)
    args = parser.parse_args()

    cfg = yaml.safe_load(open(args.config, 'r'))
    clip = ClipDataset(args.clip_dir)
    ts_list = clip.list_timestamps()

    calibs = parse_calibrated_sensor_pb_txt(str(clip.calib_pb_txt))
    pose = PoseStream(str(clip.pose_txt))

    cam_map = cfg.get('camera_map', {})
    cams = {}
    for dir_name, sensor_name in cam_map.items():
        if sensor_name not in calibs.cams:
            continue
        c = calibs.cams[sensor_name]
        cams[dir_name] = Camera(
            name=dir_name,
            K=c['K'],
            dist=c['dist'],
            width=c['width'],
            height=c['height'],
            T_base_cam=c['T_base_cam'],
        )

    save_root = Path(args.save_dir) / Path(args.clip_dir).name
    save_root.mkdir(parents=True, exist_ok=True)

    for ts in ts_list:
        t_lidar = float(ts)
        paths = clip.frame_paths_by_ts(ts)
        pts = load_pcd_xyz(paths['pcd'])
        j = json.loads(Path(paths['json']).read_text(encoding='utf-8'))
        objects = j.get('result', {}).get('data', [])

        # Depth maps per camera with time compensation to each image's timestamp
        depth_imgs = {}
        cam_dt = {}
        images_cache = {}
        for cam_name, cam in cams.items():
            img_path = paths['images'][cam_name]
            if not img_path.exists():
                continue
            try:
                t_img = float(img_path.stem)
            except Exception:
                t_img = t_lidar
            dt = abs(t_img - t_lidar)
            cam_dt[cam_name] = dt
            # compensate points from lidar time to image time
            pts_cam_time = compensate_points_to_time(pose, pts, t_lidar, t_img)
            H, W = cam.height, cam.width
            D, hits = pointcloud_to_depth(pts_cam_time, cam, H, W)
            depth_imgs[cam_name] = (D, hits)
            if args.viz_cam:
                img = cv2.imread(str(img_path))
                if img is not None:
                    images_cache[cam_name] = img

        per_object_aug = {}
        # Prepare per-camera images for drawing once and overlay multiple objects
        drawn_images = {k: v.copy() for k, v in images_cache.items()}

        for obj in objects:
            center = obj['3Dcenter']
            size = obj['3Dsize']
            cx, cy, cz = center['x'], center['y'], center['z']
            l, w, h = size['length'], size['width'], size['height']
            yaw = size.get('rz', size.get('alpha', 0.0))
            surf = sample_box_surface((cx,cy,cz), l, w, h, yaw, samples_per_face=800)
            corners = box_corners((cx,cy,cz), l, w, h, yaw)
            box = {'surface_pts': surf}

            per_cam = {}
            stats_total = {"samples": 0, "hits": 0}
            for cam_name, cam in cams.items():
                D, hits = depth_imgs[cam_name]
                if args.method == 'zbuffer':
                    r, st = visibility_via_zbuffer(box, cam, D, tau_base=cfg['visibility']['tau_base_m'], tau_scale=cfg['visibility']['tau_scale_per_m'])
                elif args.method == 'rays':
                    r, st = visibility_via_ray_sampling(box, cam, D, samples=args.samples)
                else:
                    r, st = visibility_via_zbuffer(box, cam, D, tau_base=cfg['visibility']['tau_base_m'], tau_scale=cfg['visibility']['tau_scale_per_m'])
                    if st.get('samples', 0) < cfg['visibility']['min_pixels_for_zbuffer']:
                        r, st = visibility_via_ray_sampling(box, cam, D, samples=args.samples)
                per_cam[cam_name] = r
                stats_total['samples'] += st.get('samples', 0)
                stats_total['hits'] += st.get('hits', 0)

                # Visualization overlay (draw on cached image for this camera)
                if args.viz_cam and cam_name in drawn_images:
                    img = drawn_images[cam_name]
                    draw_projected_points(img, cam, pts)
                    draw_box_projections(img, cam, corners)
                    overlay_visibility_score(img, r)

            vis_avg, vis_max, visible_views = fuse_camera_visibility(per_cam, include_noFOV_as_zero=cfg['visibility']['include_noFOV_as_zero'])
            level = to_occlusion_level(vis_avg)
            conf = visibility_confidence(stats_total['samples'], stats_total['hits'], sync_dt=0.0)

            obj_aug = {
                "visibility": {
                    "per_camera": per_cam,
                    "visibility_rate_avg": vis_avg,
                    "visibility_rate_max": vis_max,
                    "occlusion_level": level,
                    "visible_in_views": visible_views,
                    "visibility_method": args.method,
                    "visibility_confidence": conf
                },
                "sync": {
                    "compensated": True,
                    "sync_dt_sec": {cam: float(cam_dt.get(cam, 0.0)) for cam in cams.keys()},
                    "compensation_method": "ego_pose_interpolation_SE3"
                }
            }
            per_object_aug[obj['ObjectID']] = obj_aug

        out_json = save_root / f"{ts}.json"
        from .integrate_json import write_augmented_json
        write_augmented_json(str(paths['json']), str(out_json), per_object_aug)

        # Save per-camera images for this ts
        if args.viz_cam and drawn_images:
            for cam_name, img in drawn_images.items():
                out_img = save_root / 'viz' / cam_name / f"{ts}.jpg"
                save_image(img, str(out_img))

    print(f"Done: outputs saved to {save_root}")

if __name__ == '__main__':
    main()
