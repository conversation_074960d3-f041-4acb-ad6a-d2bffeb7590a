# VisionAware Annotations - Project Roadmap

## Executive Summary

The VisionAware Annotations project addresses a critical challenge in autonomous driving perception: the transition from expensive multi-modal sensor systems (cameras + LiDAR) to cost-effective vision-only solutions. Our toolkit automatically calculates visibility and occlusion attributes for 3D annotated objects, solving the "supervision signal mismatch" problem that causes false-positive detections in vision-only models.

## Problem Statement

### The Challenge
Current autonomous driving datasets are annotated using complete LiDAR point clouds, which can "see" objects that are invisible or heavily occluded from camera perspectives. When training vision-only models on these datasets, the models are incorrectly penalized for failing to detect objects they cannot actually see, leading to:

- High false-positive detection rates ("ghost objects")
- Reduced model reliability and safety
- Poor performance in real-world deployment scenarios

### The Solution
Our automated pipeline calculates per-object visibility scores using advanced computer vision techniques:
- **Z-buffer depth rendering** for accurate occlusion detection
- **Multi-camera fusion** for comprehensive visibility assessment
- **Ego-motion compensation** for temporal synchronization
- **Graded occlusion levels** for flexible training strategies

## Technical Architecture

### Core Components

#### 1. Camera Model (`camera_model.py`)
```python
@dataclass
class Camera:
    name: str
    K: np.ndarray  # 3x3 intrinsic matrix
    dist: Optional[np.ndarray]  # distortion coefficients
    width: int
    height: int
    T_base_cam: np.ndarray  # 4x4 transformation matrix
```

**Key Features:**
- Pinhole camera projection with optional distortion correction
- Robust handling of edge cases (zero depth, empty point sets)
- Base-to-camera coordinate transformation

#### 2. Depth Rendering (`depth_tools.py`)
The Z-buffer algorithm creates scene-wide depth maps:
```python
def pointcloud_to_depth(pts_base, cam, H, W, fill_min_neighbors=0):
    # Projects 3D points to 2D image coordinates
    # Maintains minimum depth per pixel (Z-buffer)
    # Returns depth image with np.inf for missing pixels
```

#### 3. Visibility Calculation (`per_camera_pixel_ratio.py`)
Primary visibility estimation using depth comparison:
```python
def visibility_via_zbuffer(box3d, cam, depth_img, tau_base=0.3, tau_scale=0.02):
    # Projects object surface points to image
    # Compares projected depth with scene depth
    # Applies adaptive tolerance based on distance
    # Returns visibility ratio and statistics
```

#### 4. Multi-Camera Fusion (`fusion.py`)
Combines visibility scores across all camera views:
```python
def fuse_camera_visibility(per_cam, include_noFOV_as_zero=False):
    # Aggregates per-camera visibility scores
    # Returns average, maximum, and list of visible views
    # Handles cameras with no field-of-view coverage
```

### Configuration System (`configs/default.yaml`)

The system is highly configurable with parameters for:

**Synchronization:**
- `max_sync_dt_ms: 30` - Maximum time offset for sensor alignment
- `pose_required: true` - Enforce ego-motion compensation

**Visibility Calculation:**
- `method_primary: zbuffer` - Primary algorithm (Z-buffer)
- `method_fallback: rays` - Fallback method (ray sampling)
- `tau_base_m: 0.3` - Base depth tolerance
- `tau_scale_per_m: 0.02` - Distance-scaled tolerance
- `per_view_visible_thresh: 0.15` - Minimum visibility threshold

**Training Integration:**
- `drop_if_level_ge: 4` - Filter highly occluded objects
- `min_vis_avg: 0.5` - Minimum average visibility
- `loss_weight_by_confidence: true` - Weight losses by visibility

## Current Implementation Status

### ✅ Completed Components

1. **Core Algorithms**
   - Camera projection model with distortion handling
   - Z-buffer depth rendering
   - Visibility calculation via depth comparison
   - Surface ray sampling fallback method
   - Multi-camera fusion algorithms

2. **Data Infrastructure**
   - Dataset loading framework (`robobus_vis/io/`)
   - Calibration parsing for camera parameters
   - Pose stream handling for ego-motion
   - Configuration management system

3. **Modular Architecture**
   - Clean separation of concerns across modules
   - Extensible design for additional algorithms
   - Comprehensive error handling and edge cases

### 🔄 In Progress

1. **Pipeline Integration**
   - Main execution pipeline (`run_clip.py`) - basic framework complete
   - Batch processing capabilities
   - JSON annotation integration

2. **Visualization Tools**
   - Debug visualization for depth maps
   - Occlusion level visualization
   - Multi-camera view rendering

### 📋 Planned Development

1. **Advanced Features**
   - Temporal consistency across frames
   - Dynamic object tracking integration
   - Adaptive sampling strategies

2. **Performance Optimization**
   - GPU acceleration for depth rendering
   - Parallel processing for multi-camera systems
   - Memory-efficient large dataset handling

3. **Training Integration**
   - Direct integration with BEVFusion framework
   - Automated dataset filtering and weighting
   - Validation metrics and benchmarking

## Development Phases

### Phase 1: Foundation (Current)
**Timeline:** Completed
**Deliverables:**
- ✅ Core visibility algorithms
- ✅ Camera projection and calibration
- ✅ Configuration system
- ✅ Modular architecture

### Phase 2: Pipeline Completion
**Timeline:** 2-3 weeks
**Deliverables:**
- Complete end-to-end processing pipeline
- Batch processing for large datasets
- JSON annotation output with visibility attributes
- Basic visualization tools

**Key Tasks:**
- Implement full `run_clip.py` functionality
- Add surface point sampling for 3D boxes
- Integrate pose-based motion compensation
- Create output format specification

### Phase 3: Optimization & Validation
**Timeline:** 3-4 weeks
**Deliverables:**
- Performance-optimized algorithms
- Comprehensive validation suite
- Benchmark against ground truth data
- Documentation and user guides

**Key Tasks:**
- GPU acceleration implementation
- Validation against manual annotations
- Performance profiling and optimization
- Error analysis and edge case handling

### Phase 4: Training Integration
**Timeline:** 4-5 weeks
**Deliverables:**
- BEVFusion framework integration
- Automated training pipeline
- Comparative performance analysis
- Production deployment tools

**Key Tasks:**
- Implement training data filtering
- Create visibility-weighted loss functions
- Develop evaluation metrics
- Production deployment scripts

## Technical Specifications

### Input Requirements
- **Point Cloud Data:** `.pcd` files with 3D scene geometry
- **Camera Images:** Multi-view RGB images (7 cameras typical)
- **Annotations:** 3D bounding boxes in JSON format
- **Calibration:** Camera intrinsics and extrinsics
- **Pose Data:** Vehicle trajectory for motion compensation

### Output Format
Enhanced annotations with visibility attributes:
```json
{
  "objects": [
    {
      "id": "obj_001",
      "category": "car",
      "box3d": {...},
      "visibility": {
        "avg_score": 0.75,
        "max_score": 0.92,
        "per_camera": {
          "60_front": 0.85,
          "120_front": 0.92,
          "120_left": 0.45,
          ...
        },
        "occlusion_level": 2,
        "visible_views": ["60_front", "120_front"]
      }
    }
  ]
}
```

### Performance Targets
- **Processing Speed:** 10-15 frames per second
- **Accuracy:** >95% correlation with manual annotations
- **Memory Usage:** <8GB for typical datasets
- **Scalability:** Support for 1000+ frame sequences

## Risk Assessment & Mitigation

### Technical Risks
1. **Depth Map Quality**
   - *Risk:* Sparse point clouds may produce incomplete depth maps
   - *Mitigation:* Implement ray sampling fallback method

2. **Calibration Accuracy**
   - *Risk:* Poor calibration affects projection accuracy
   - *Mitigation:* Robust calibration validation and error detection

3. **Temporal Synchronization**
   - *Risk:* Sensor timing misalignment affects accuracy
   - *Mitigation:* Configurable sync tolerances and pose interpolation

### Project Risks
1. **Performance Requirements**
   - *Risk:* Processing speed may not meet production needs
   - *Mitigation:* GPU acceleration and algorithmic optimization

2. **Integration Complexity**
   - *Risk:* BEVFusion integration may require significant changes
   - *Mitigation:* Modular design and standardized output formats

## Success Metrics

### Technical Metrics
- **Visibility Accuracy:** Correlation with manual annotations >0.95
- **Processing Efficiency:** <100ms per object per camera
- **False Positive Reduction:** >50% reduction in ghost detections
- **Model Performance:** Improved mAP scores on validation sets

### Business Metrics
- **Cost Reduction:** 70% reduction in sensor costs vs LiDAR systems
- **Deployment Speed:** 3x faster model training and validation
- **Scalability:** Support for 10x larger datasets
- **Reliability:** Production-ready autonomous driving perception

## Conclusion

The VisionAware Annotations project represents a critical step in the evolution of autonomous driving perception systems. By solving the supervision signal mismatch problem, we enable the development of reliable, cost-effective vision-only perception systems that can compete with expensive multi-modal approaches.

Our systematic approach, combining advanced computer vision algorithms with robust engineering practices, positions this project for successful deployment in production autonomous driving systems. The modular architecture ensures extensibility for future enhancements while maintaining the reliability required for safety-critical applications.

---

*This roadmap is a living document that will be updated as the project evolves and new requirements emerge.*
## Detail
ed Technical Architecture

### Algorithm Deep Dive

#### Z-Buffer Visibility Calculation

The core visibility algorithm uses a Z-buffer (depth buffer) approach, which is the gold standard for occlusion detection in computer graphics and robotics. Here's how it works in our implementation:

**Step 1: Scene Depth Rendering** (`depth_tools.py`)
```python
def pointcloud_to_depth(pts_base: np.ndarray, cam, H: int, W: int, fill_min_neighbors: int = 0):
    """
    Creates a depth map from the complete point cloud scene.
    Uses Z-buffer algorithm to maintain minimum depth per pixel.
    """
    # Project all scene points to camera coordinates
    uvz = cam.project(pts_base)
    u, v, z = np.round(uvz[:,0]).astype(int), np.round(uvz[:,1]).astype(int), uvz[:,2]
    
    # Filter points within image bounds and positive depth
    mask = (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u, v, z = u[mask], v[mask], z[mask]
    
    # Initialize depth buffer with infinity (no depth)
    D = np.full((H, W), np.inf, dtype=np.float32)
    
    # Z-buffer: keep minimum depth per pixel
    for ui, vi, zi in zip(u, v, z):
        if zi < D[vi, ui]:
            D[vi, ui] = zi
```

**Step 2: Object Visibility Assessment** (`per_camera_pixel_ratio.py`)
```python
def visibility_via_zbuffer(box3d, cam, depth_img: np.ndarray, tau_base=0.3, tau_scale=0.02):
    """
    Determines object visibility by comparing object surface depths 
    with scene depth map.
    """
    # Project object surface points to image
    pts = box3d['surface_pts']  # Pre-sampled surface points
    uvz = cam.project(pts)
    u, v, z = np.round(uvz[:,0]).astype(int), np.round(uvz[:,1]).astype(int), uvz[:,2]
    
    # Filter points within image bounds
    in_img = (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u, v, z = u[in_img], v[in_img], z[in_img]
    
    # Get scene depth at projected locations
    D = depth_img[v, u]
    
    # Adaptive depth tolerance (accounts for discretization and noise)
    tau = np.maximum(tau_base, tau_scale * z)
    
    # Visibility test: object is visible if its depth is close to scene depth
    visible = (np.isfinite(D)) & ((z - D) <= tau)
    
    return float(visible.sum()) / float(len(visible))
```

**Key Algorithmic Insights:**
- **Adaptive Tolerance:** The depth comparison uses distance-dependent tolerance (`tau_scale * z`) to account for discretization errors at far distances
- **Robust Handling:** Infinite depth values represent "no scene geometry," allowing objects to be visible in empty space
- **Surface Sampling:** Objects are represented by surface point samples rather than volume filling for computational efficiency

#### Camera Projection Model

The camera model implements a standard pinhole camera with optional distortion correction:

**Mathematical Foundation:**
```
World → Camera: P_cam = T_base_cam * P_world
Camera → Image: [u, v] = K * [X/Z, Y/Z, 1]^T
```

**Implementation Details** (`camera_model.py`):
```python
def project(self, pts_base: np.ndarray):
    """
    Projects 3D points from base frame to image coordinates.
    Handles edge cases and numerical stability.
    """
    if pts_base.size == 0:
        return np.zeros((0,3), dtype=float)
    
    # Homogeneous coordinates for transformation
    pts_h = np.concatenate([pts_base[:,:3], np.ones((pts_base.shape[0],1))], axis=1)
    
    # Transform to camera coordinates
    P = (self.T_base_cam @ pts_h.T).T[:, :3]
    z = P[:,2]
    
    # Safe division with zero handling
    x = np.divide(P[:,0], z, out=np.zeros_like(z), where=z!=0)
    y = np.divide(P[:,1], z, out=np.zeros_like(z), where=z!=0)
    
    # Apply camera intrinsics
    u = self.K[0,0]*x + self.K[0,2]  # fx * x + cx
    v = self.K[1,1]*y + self.K[1,2]  # fy * y + cy
    
    return np.stack([u, v, z], axis=1)
```

**Robustness Features:**
- **Zero Depth Handling:** Uses `np.divide` with safe division to prevent division by zero
- **Empty Input Handling:** Returns appropriately shaped empty arrays
- **Numerical Stability:** Adds small epsilon values where needed

#### Multi-Camera Fusion Algorithm

The fusion algorithm combines visibility scores from multiple cameras to create a comprehensive assessment:

**Fusion Strategy** (`fusion.py`):
```python
def fuse_camera_visibility(per_cam: Dict[str, float], include_noFOV_as_zero=False):
    """
    Combines per-camera visibility scores using multiple metrics.
    Returns average, maximum, and list of cameras with good visibility.
    """
    vals = []
    visible_views = []
    
    for cam, r in per_cam.items():
        if r is None:  # Object not in camera's field of view
            if include_noFOV_as_zero:
                vals.append(0.0)
            continue
        
        vals.append(r)
        if r >= 0.15:  # Configurable visibility threshold
            visible_views.append(cam)
    
    if not vals:
        return 0.0, 0.0, []
    
    return sum(vals)/len(vals), max(vals), visible_views
```

**Occlusion Level Mapping:**
```python
def to_occlusion_level(vis_avg: float) -> int:
    """Maps continuous visibility scores to discrete occlusion levels."""
    if vis_avg >= 0.99: return 1  # No occlusion
    if vis_avg >= 0.75: return 2  # Low occlusion  
    if vis_avg >= 0.5:  return 3  # Medium occlusion
    return 4                      # High occlusion
```

#### Surface Ray Sampling (Fallback Method)

When Z-buffer method fails (e.g., sparse point clouds), the system falls back to ray sampling:

**Ray-Based Visibility** (`surface_ray_sampling.py`):
```python
def visibility_via_ray_sampling(box3d, cam, depth_img: np.ndarray, samples=2048):
    """
    Estimates visibility using surface ray sampling.
    More robust for sparse scenes but computationally more expensive.
    """
    pts = box3d['surface_pts']
    
    # Subsample for performance
    if samples > 0 and len(pts) > samples:
        idx = np.random.choice(len(pts), size=samples, replace=False)
        pts = pts[idx]
    
    # Project and test visibility
    uvz = cam.project(pts)
    # ... (similar projection logic)
    
    # Ray-based occlusion test
    visible = (~np.isfinite(D)) | (D >= z)  # Visible if no depth or depth >= object
    
    return float(visible.sum()) / float(len(visible))
```

### Data Flow Architecture

```
Input Data Sources:
├── Point Cloud (.pcd) ──┐
├── Camera Images ───────┤
├── 3D Annotations ──────┼──► Scene Reconstruction
├── Camera Calibration ──┤
└── Pose Data ───────────┘

Scene Processing:
├── Ego-Motion Compensation ──► Synchronized Scene
├── Multi-Camera Depth Rendering ──► Depth Maps
└── Surface Point Sampling ──► Object Representations

Visibility Calculation:
├── Per-Camera Z-Buffer Analysis ──► Individual Scores
├── Multi-Camera Fusion ──► Aggregated Scores
└── Occlusion Level Classification ──► Discrete Labels

Output Generation:
├── Enhanced JSON Annotations ──► Training Data
├── Visualization Assets ──► Debug/Validation
└── Statistics Reports ──► Quality Metrics
```

### Performance Optimization Strategies

#### Memory Management
- **Streaming Processing:** Process frames sequentially to avoid loading entire datasets
- **Efficient Data Structures:** Use NumPy arrays with appropriate dtypes
- **Garbage Collection:** Explicit cleanup of large temporary arrays

#### Computational Optimization
- **Vectorized Operations:** Leverage NumPy's vectorized operations for bulk processing
- **Early Termination:** Skip processing for objects clearly outside camera FOV
- **Adaptive Sampling:** Reduce surface point density for distant objects

#### Parallel Processing Opportunities
- **Multi-Camera Parallelism:** Process different cameras simultaneously
- **Frame-Level Parallelism:** Process multiple frames in parallel
- **GPU Acceleration:** Potential CUDA implementation for depth rendering

### Error Handling and Edge Cases

#### Geometric Edge Cases
- **Degenerate Transformations:** Detect and handle singular transformation matrices
- **Extreme Aspect Ratios:** Handle very thin or flat objects appropriately
- **Boundary Conditions:** Robust handling of objects at image boundaries

#### Data Quality Issues
- **Sparse Point Clouds:** Automatic fallback to ray sampling method
- **Calibration Errors:** Validation and error reporting for bad calibrations
- **Temporal Misalignment:** Configurable tolerance for sync issues

#### Numerical Stability
- **Division by Zero:** Safe division operations throughout
- **Floating Point Precision:** Appropriate epsilon values for comparisons
- **Overflow Prevention:** Range checking for large coordinate values

This detailed technical documentation provides the foundation for understanding, maintaining, and extending the VisionAware Annotations system. The modular design and robust error handling ensure reliable operation across diverse autonomous driving scenarios.