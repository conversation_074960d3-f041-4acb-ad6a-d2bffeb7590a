import numpy as np


def visibility_via_zbuffer(box3d, cam, depth_img: np.ndarray, tau_base=0.3, tau_scale=0.02):
    """Compute visible pixel ratio inside projected box mask by comparing depth.
    box3d: dict with center(x,y,z), size(l,w,h), yaw.
    cam: Camera
    depth_img: np.ndarray (H,W) with np.inf for missing
    Returns: (ratio, stats)
    """
    H, W = depth_img.shape
    # Sample box surface and project
    pts = box3d['surface_pts']  # precomputed Nx3
    uvz = cam.project(pts)
    u = np.round(uvz[:,0]).astype(int)
    v = np.round(uvz[:,1]).astype(int)
    z = uvz[:,2]
    in_img = (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u, v, z = u[in_img], v[in_img], z[in_img]
    if len(u) == 0:
        return 0.0, {"samples": 0, "hits": 0}
    D = depth_img[v, u]
    tau = np.maximum(tau_base, tau_scale * z)
    visible = (np.isfinite(D)) & ((z - D) <= tau)
    ratio = float(visible.sum()) / float(len(visible))
    stats = {"samples": int(len(visible)), "hits": int(np.isfinite(D).sum()), "avg_z": float(np.mean(z))}
    return ratio, stats
