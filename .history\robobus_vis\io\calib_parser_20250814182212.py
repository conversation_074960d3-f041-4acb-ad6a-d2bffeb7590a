from typing import Dict, Any
from pathlib import Path
import re
import numpy as np
from ..calib.transforms import quat_to_R, make_T

"""Robust parser for calibrated_sensor.pb.txt (Protobuf TextFormat) with nested blocks.
- Extract full sensor_info blocks by brace matching
- Parse name, extrinsic (translation xyz + rotation quaternion any order), intrinsic (width/height + 9 matrix), distcoeff
"""

NAME_RE = re.compile(r"name:\s*\"([^\"]+)\"")
WIDTH_RE = re.compile(r"width:\s*(\d+)")
HEIGHT_RE = re.compile(r"height:\s*(\d+)")
MATRIX_RE = re.compile(r"matrix:\s*([-+eE0-9\.]+)")
DISTORT_RE = re.compile(r"distort_matrix:\s*([-+eE0-9\.]+)")

# translation / rotation (order-agnostic inside the braces)
TX_BLOCK_RE = re.compile(r"translation\s*\{([\s\S]*?)\}")
ROT_BLOCK_RE = re.compile(r"rotation\s*\{([\s\S]*?)\}")
VAL_RE = re.compile(r"([wxyz]):\s*([-+eE0-9\.]+)")
XYZ_RE = re.compile(r"([xyz]):\s*([-+eE0-9\.]+)")


def _extract_blocks(text: str, key: str):
    blocks = []
    i = 0
    n = len(text)
    token = key
    while i < n:
        j = text.find(token, i)
        if j == -1:
            break
        # find first '{' after token
        k = text.find('{', j)
        if k == -1:
            break
        depth = 0
        end = k
        while end < n:
            if text[end] == '{':
                depth += 1
            elif text[end] == '}':
                depth -= 1
                if depth == 0:
                    end += 1
                    break
            end += 1
        blocks.append(text[j:end])
        i = end
    return blocks


def parse_calibrated_sensor_pb_txt(path: str) -> "Calibrations":
    text = Path(path).read_text(encoding='utf-8', errors='ignore')
    cams: Dict[str, Dict[str, Any]] = {}
    for block in _extract_blocks(text, 'sensor_info'):
        name_m = NAME_RE.search(block)
        if not name_m:
            continue
        name = name_m.group(1)
        # translation
        tx, ty, tz = 0.0, 0.0, 0.0
        t_blk = TX_BLOCK_RE.search(block)
        if t_blk:
            parts = dict((m.group(1), float(m.group(2))) for m in XYZ_RE.finditer(t_blk.group(1)))
            tx, ty, tz = parts.get('x', 0.0), parts.get('y', 0.0), parts.get('z', 0.0)
        # rotation quaternion (accept any order)
        rx = ry = rz = 0.0
        rw = 1.0
        r_blk = ROT_BLOCK_RE.search(block)
        if r_blk:
            parts = dict((m.group(1), float(m.group(2))) for m in VAL_RE.finditer(r_blk.group(1)))
            rx, ry, rz, rw = parts.get('x', 0.0), parts.get('y', 0.0), parts.get('z', 0.0), parts.get('w', 1.0)
        R = quat_to_R(rx, ry, rz, rw)
        T_base_cam = make_T(R, np.array([tx, ty, tz], dtype=float))
        # intrinsics
        width = int(WIDTH_RE.search(block).group(1)) if WIDTH_RE.search(block) else 0
        height = int(HEIGHT_RE.search(block).group(1)) if HEIGHT_RE.search(block) else 0
        mats = [float(m) for m in MATRIX_RE.findall(block)]
        K = np.eye(3)
        if len(mats) >= 9:
            K = np.array(mats[:9], dtype=float).reshape(3,3)
        dcoeff = [float(d) for d in DISTORT_RE.findall(block)]
        dist = np.array(dcoeff, dtype=float) if dcoeff else None
        cams[name] = {
            "name": name,
            "K": K,
            "dist": dist,
            "width": width,
            "height": height,
            "T_base_cam": T_base_cam,
        }
    return Calibrations(cams)


class Calibrations:
    def __init__(self, cams: Dict[str, Dict[str, Any]]):
        self.cams = cams

    def for_camera(self, cam_name: str) -> Dict[str, Any]:
        return self.cams[cam_name]

    def cameras(self):
        return list(self.cams.keys())
