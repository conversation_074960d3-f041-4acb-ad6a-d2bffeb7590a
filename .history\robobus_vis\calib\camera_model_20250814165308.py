from dataclasses import dataclass
from typing import Optional
import numpy as np

@dataclass
class Camera:
    name: str
    K: np.ndarray  # 3x3
    dist: Optional[np.ndarray]
    width: int
    height: int
    T_base_cam: np.ndarray  # 4x4 (base->cam)

    def project(self, pts_base: np.ndarray):
        """Placeholder projection using pinhole without distortion."""
        # Transform base->cam
        pts_h = np.concatenate([pts_base[:,:3], np.ones((pts_base.shape[0],1))], axis=1)
        P = (self.T_base_cam @ pts_h.T).T[:, :3]
        z = P[:,2]
        x = P[:,0] / (z + 1e-9)
        y = P[:,1] / (z + 1e-9)
        u = self.K[0,0]*x + self.K[0,2]
        v = self.K[1,1]*y + self.K[1,2]
        return np.stack([u, v, z], axis=1)
